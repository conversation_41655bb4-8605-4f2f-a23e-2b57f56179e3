"""Image Service for CT image preparation and conversion.

This service handles image data conversion from various sources to display-ready
formats for the NiceGUI interactive image component. It provides a bridge between
the PinnacleAPI and the UI layer.
"""

import base64
from io import BytesIO
from pathlib import Path
from typing import Optional, Tuple
import logging

import numpy as np
from PIL import Image

from pinnacle_dicom_converter.services.pinnacle_api import PinnacleAP<PERSON>
from pinnacle_dicom_converter.core.models.image_set import ImageSet

logger = logging.getLogger(__name__)


class ImageService:
    """Service for handling CT image data conversion and preparation.
    
    This service converts raw image data from PinnacleAPI into display-ready
    formats for the NiceGUI UI. It handles:
    - Image data loading from PinnacleAPI
    - Orientation transformations (axial, sagittal, coronal)
    - Window/level adjustments
    - Conversion to base64 data URLs for web display
    - Placeholder image serving during development
    
    Attributes:
        pinnacle_api: Optional PinnacleAPI instance for data access
        placeholder_image_path: Path to placeholder CT slice image
    """
    
    def __init__(self, pinnacle_api: Optional[PinnacleAPI] = None):
        """Initialize the Image Service.
        
        Args:
            pinnacle_api: Optional PinnacleAPI instance for real data access
        """
        self.pinnacle_api = pinnacle_api
        self.placeholder_image_path = Path(__file__).parent.parent.parent.parent.parent / "guides" / "sample_ct_slice.jpg"
        
    def get_placeholder_image_url(self) -> str:
        """Get the placeholder CT slice image as a data URL.
        
        Returns:
            Base64 data URL of the placeholder image
            
        Raises:
            FileNotFoundError: If placeholder image file doesn't exist
        """
        if not self.placeholder_image_path.exists():
            raise FileNotFoundError(f"Placeholder image not found: {self.placeholder_image_path}")
            
        # Load and convert placeholder image to data URL
        with open(self.placeholder_image_path, "rb") as f:
            image_data = f.read()
            
        # Convert to base64 data URL
        image_base64 = base64.b64encode(image_data).decode()
        return f"data:image/jpeg;base64,{image_base64}"
    
    def get_slice_data_url(
        self,
        image_set: Optional[ImageSet],
        slice_index: int,
        orientation: str = "axial",
        window_width: int = 1400,
        window_level: int = 1000
    ) -> Optional[str]:
        """Get slice data as a base64 data URL.

        Args:
            image_set: ImageSet object from PinnacleAPI (None for placeholder)
            slice_index: Index of the slice to display
            orientation: View orientation ('axial', 'sagittal', 'coronal')
            window_width: CT window width for display
            window_level: CT window level (center) for display

        Returns:
            Base64 data URL of the processed image, or None if no data
        """
        if image_set is None:
            return self.get_placeholder_image_url()

        # Check if we have real image data
        if not hasattr(image_set, 'pixel_data') or image_set.pixel_data is None:
            return self.get_placeholder_image_url()

        try:
            # Extract slice data based on orientation and index
            slice_data = self._extract_slice_data(image_set, slice_index, orientation)
            if slice_data is None:
                return self.get_placeholder_image_url()

            # Apply window/level adjustments
            windowed_data = self._apply_window_level(slice_data, window_width, window_level)

            # Convert to PIL Image
            pil_image = Image.fromarray(windowed_data, mode='L')

            # Convert to base64 data URL
            buffer = BytesIO()
            pil_image.save(buffer, format='PNG')
            buffer.seek(0)
            image_base64 = base64.b64encode(buffer.read()).decode()
            return f"data:image/png;base64,{image_base64}"

        except Exception as e:
            logger.error(f"Failed to process image data: {e}")
            return self.get_placeholder_image_url()
    
    def get_image_dimensions(self, image_set: Optional[ImageSet]) -> Tuple[int, int, int]:
        """Get image dimensions (width, height, depth).

        Args:
            image_set: ImageSet object from PinnacleAPI

        Returns:
            Tuple of (width, height, depth) dimensions
        """
        if image_set is None:
            # Return mock dimensions for placeholder
            return (512, 512, 100)

        # Extract real dimensions from ImageSet
        if hasattr(image_set, 'x_dim') and hasattr(image_set, 'y_dim') and hasattr(image_set, 'z_dim'):
            return (image_set.x_dim, image_set.y_dim, image_set.z_dim)

        # Fallback to mock dimensions
        return (512, 512, 100)
    
    def get_pixel_spacing(self, image_set: Optional[ImageSet]) -> Tuple[float, float, float]:
        """Get pixel spacing (x, y, z) in millimeters.

        Args:
            image_set: ImageSet object from PinnacleAPI

        Returns:
            Tuple of (x_spacing, y_spacing, z_spacing) in mm
        """
        if image_set is None:
            # Return mock spacing for placeholder
            return (1.0, 1.0, 3.0)

        # Extract real spacing from ImageSet
        if hasattr(image_set, 'x_pixdim') and hasattr(image_set, 'y_pixdim') and hasattr(image_set, 'z_pixdim'):
            # Convert from cm to mm (Pinnacle uses cm)
            x_spacing = image_set.x_pixdim * 10.0
            y_spacing = image_set.y_pixdim * 10.0
            z_spacing = image_set.z_pixdim * 10.0
            return (x_spacing, y_spacing, z_spacing)

        # Fallback to mock spacing
        return (1.0, 1.0, 3.0)
    
    def get_image_origin(self, image_set: Optional[ImageSet]) -> Tuple[float, float, float]:
        """Get image origin (x, y, z) in millimeters.

        Args:
            image_set: ImageSet object from PinnacleAPI

        Returns:
            Tuple of (x_origin, y_origin, z_origin) in mm
        """
        if image_set is None:
            # Return mock origin for placeholder
            return (0.0, 0.0, 0.0)

        # Extract real origin from ImageSet
        if hasattr(image_set, 'x_start') and hasattr(image_set, 'y_start') and hasattr(image_set, 'z_start'):
            # Convert from cm to mm (Pinnacle uses cm)
            x_origin = image_set.x_start * 10.0
            y_origin = image_set.y_start * 10.0
            z_origin = image_set.z_start * 10.0
            return (x_origin, y_origin, z_origin)

        # Fallback to mock origin
        return (0.0, 0.0, 0.0)
    
    def pixel_to_world_coordinates(
        self,
        pixel_x: float,
        pixel_y: float,
        slice_index: int,
        image_set: Optional[ImageSet],
        orientation: str = "axial"
    ) -> Tuple[float, float, float]:
        """Convert pixel coordinates to world coordinates.
        
        Args:
            pixel_x: X pixel coordinate
            pixel_y: Y pixel coordinate
            slice_index: Current slice index
            image_set: ImageSet object from PinnacleAPI
            orientation: Current view orientation
            
        Returns:
            Tuple of (x, y, z) world coordinates in mm
        """
        if image_set is None:
            # Mock coordinate conversion for placeholder
            spacing = self.get_pixel_spacing(image_set)
            origin = self.get_image_origin(image_set)
            
            # Simple conversion (assumes axial orientation)
            world_x = origin[0] + pixel_x * spacing[0]
            world_y = origin[1] + pixel_y * spacing[1]
            world_z = origin[2] + slice_index * spacing[2]
            
            return (world_x, world_y, world_z)
            
        # Future implementation will handle real coordinate transformations
        # including proper orientation handling
        spacing = self.get_pixel_spacing(image_set)
        origin = self.get_image_origin(image_set)
        
        world_x = origin[0] + pixel_x * spacing[0]
        world_y = origin[1] + pixel_y * spacing[1]
        world_z = origin[2] + slice_index * spacing[2]
        
        return (world_x, world_y, world_z)
    
    def _apply_window_level(self, image_data: np.ndarray, window_width: int, window_level: int) -> np.ndarray:
        """Apply window/level adjustments to image data.
        
        Args:
            image_data: Raw image data as numpy array
            window_width: Window width for display
            window_level: Window level (center) for display
            
        Returns:
            Windowed image data as 8-bit numpy array
        """
        # Calculate window bounds
        window_min = window_level - window_width // 2
        window_max = window_level + window_width // 2
        
        # Apply windowing
        windowed = np.clip(image_data, window_min, window_max)
        
        # Scale to 0-255 range
        windowed = ((windowed - window_min) / (window_max - window_min) * 255).astype(np.uint8)
        
        return windowed

    def _extract_slice_data(self, image_set: ImageSet, slice_index: int, orientation: str = "axial") -> Optional[np.ndarray]:
        """Extract slice data from image set based on orientation.

        Args:
            image_set: ImageSet object with pixel_data
            slice_index: Index of the slice to extract
            orientation: View orientation ('axial', 'sagittal', 'coronal')

        Returns:
            2D numpy array of slice data, or None if extraction fails
        """
        if not hasattr(image_set, 'pixel_data') or image_set.pixel_data is None:
            return None

        try:
            pixel_data = image_set.pixel_data

            # Ensure we have 3D data
            if pixel_data.ndim != 3:
                logger.error(f"Expected 3D pixel data, got {pixel_data.ndim}D")
                return None

            # Extract slice based on orientation. Numpy arrays are indexed as (Z, Y, X)
            if orientation.lower() == 'axial':
                # Axial view: extract Z slice
                if slice_index >= pixel_data.shape[0]:
                    slice_index = pixel_data.shape[0] - 1
                slice_data = pixel_data[slice_index, :, :]
            elif orientation.lower() == 'sagittal':
                # Sagittal view: extract X slice
                if slice_index >= pixel_data.shape[2]:
                    slice_index = pixel_data.shape[2] - 1
                slice_data = pixel_data[:, :, slice_index]
            elif orientation.lower() == 'coronal':
                # Coronal view: extract Y slice
                if slice_index >= pixel_data.shape[1]:
                    slice_index = pixel_data.shape[1] - 1
                slice_data = pixel_data[:, slice_index, :]
            else:
                logger.error(f"Unsupported orientation: {orientation}")
                return None

            return slice_data.astype(np.float32)

        except Exception as e:
            logger.error(f"Failed to extract slice data: {e}")
            return None
