"""Main application view with layout and ViewModel integration."""

from nicegui import ui

from pinnacle_dicom_converter.ui.views.header_toolbar import create_header_toolbar
from pinnacle_dicom_converter.ui.views.left_drawer import create_left_drawer, setup_refresh_callbacks as setup_nav_refresh
from pinnacle_dicom_converter.ui.views.right_drawer import create_right_drawer, setup_refresh_callbacks as setup_overlay_refresh
from pinnacle_dicom_converter.ui.views.ct_viewer import create_ct_viewer, setup_refresh_callbacks as setup_image_refresh
from pinnacle_dicom_converter.ui.viewmodels.main_viewmodel import MainViewModel
from pinnacle_dicom_converter.ui.services.pinnacle_service import PinnacleService


def create_main_view() -> None:
    """Create the main application view with all components.

    This function sets up the complete MVVM architecture including:
    - MainViewModel initialization with real data services
    - Header toolbar (Phase 2)
    - Left drawer navigation (Phase 3)
    - Center CT viewer (Phase 4)
    - Right drawer controls (Phase 5)
    """
    # Initialize services and main ViewModel
    pinnacle_service = PinnacleService()
    main_vm = MainViewModel(pinnacle_service)

    # Header toolbar with icon buttons and menus (Phase 2)
    create_header_toolbar(main_vm.toolbar_vm)

    # Left drawer with navigation grids (Phase 3)
    create_left_drawer(main_vm.navigation_vm)
    setup_nav_refresh(main_vm.navigation_vm)

    # Center content area (CT Viewer - Phase 4)
    create_ct_viewer(main_vm.image_vm)
    setup_image_refresh(main_vm.image_vm)

    # Right drawer with control panels (Phase 5)
    create_right_drawer(main_vm.navigation_vm, main_vm.image_vm, main_vm.overlay_vm)
    setup_overlay_refresh(main_vm.overlay_vm)

    # Auto-load test data for development
    success = main_vm.auto_load_test_data()
    if success:
        ui.notify("Test data loaded successfully", type="positive")
    else:
        ui.notify("Failed to load test data", type="warning")