"""ROI control panel for managing ROI visibility and appearance."""

from nicegui import ui

from ...viewmodels.overlay_viewmodel import OverlayViewModel


def create_roi_panel(overlay_vm: OverlayViewModel) -> None:
    """Create ROI control panel with visibility and color controls.

    Provides an interactive table for managing ROI display properties:
    - Visibility toggles
    - Color selection
    - ROI information

    Args:
        overlay_vm: OverlayViewModel instance managing overlay state
    """
    with ui.column().classes("w-full gap-3 p-3"):
        # ui.label("ROI Controls").classes("text-h6 font-medium mb-2")

        # Info card
        with ui.card().classes("w-full bg_secondary p-3 mb-2"):
            roi_count_label = ui.label(f"ROIs: {len(overlay_vm.rois)}").classes(
                "text-sm"
            )
            visible_count_label = ui.label(
                f"Visible: {len(overlay_vm.visible_rois)}"
            ).classes("text-sm text-gray-400")

        # ROI list
        create_roi_list(overlay_vm, roi_count_label, visible_count_label)


@ui.refreshable
def create_roi_list(overlay_vm: OverlayViewModel, roi_count_label, visible_count_label):
    """Create refreshable ROI list with controls.

    Args:
        overlay_vm: OverlayViewModel instance
        roi_count_label: Label to update with ROI count
        visible_count_label: Label to update with visible count
    """
    if not overlay_vm.rois:
        with ui.card().classes("w-full bg_secondary p-4"):
            ui.label("No ROIs loaded").classes("text-gray-500 text-center")
        return

    # Create expandable cards for each ROI
    for roi in overlay_vm.rois:
        with ui.expansion(roi.name, icon="polyline").classes(
            "w-full bg_secondary"
        ).props("dense"):
            with ui.column().classes("w-full gap-2 p-2"):
                # Visibility toggle
                with ui.row().classes("w-full items-center gap-2"):
                    ui.label("Visible:").classes("text-sm")
                    visibility_switch = ui.switch(value=roi.visible).props("dense")

                    def make_toggle_handler(roi_id):
                        def handler():
                            overlay_vm.toggle_roi_visibility(roi_id)
                            # Update count labels
                            roi_count_label.text = f"ROIs: {len(overlay_vm.rois)}"
                            visible_count_label.text = (
                                f"Visible: {len(overlay_vm.visible_rois)}"
                            )
                            create_roi_list.refresh()

                        return handler

                    visibility_switch.on("update:model-value", make_toggle_handler(roi.roi_id))

                # Color picker
                with ui.row().classes("w-full items-center gap-2"):
                    ui.label("Color:").classes("text-sm")
                    color_input = ui.color_input(
                        label="", value=roi.color
                    ).classes("flex-1").props("dense")

                    def make_color_handler(roi_id):
                        def handler(e):
                            overlay_vm.change_roi_color(roi_id, e.value)
                            create_roi_list.refresh()

                        return handler

                    color_input.on("update:model-value", make_color_handler(roi.roi_id))

                # ROI info
                ui.label(f"ID: {roi.roi_id}").classes("text-xs text-gray-400")


def setup_refresh_callback(overlay_vm: OverlayViewModel) -> None:
    """Set up refresh callback for the ROI panel.

    Args:
        overlay_vm: OverlayViewModel instance
    """
    overlay_vm.refresh_rois = create_roi_list.refresh
