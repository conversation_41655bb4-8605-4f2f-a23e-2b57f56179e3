"""Overlay ViewModel for managing ROI/POI/Beam/Dose overlays."""

from typing import List, Optional, Callable
import logging

from ..models import (
    ROIDisplayModel,
    POIDisplayModel,
    BeamDisplayModel,
    DoseDisplayModel,
    IsodoseDisplayModel,
)
from ..services.pinnacle_service import PinnacleService

logger = logging.getLogger(__name__)


class OverlayViewModel:
    """ViewModel for managing ROI/POI/Beam/Dose overlays.

    This ViewModel maintains the state of all overlays and provides methods
    to manipulate their visibility, colors, and other display properties.

    Attributes:
        rois: List of ROI display models
        pois: List of POI display models
        beams: List of beam display models
        dose_model: Dose display model with isodose lines
    """

    def __init__(self, pinnacle_service: Optional[PinnacleService] = None):
        """Initialize the overlay ViewModel with service injection.

        Args:
            pinnacle_service: Service for accessing Pinnacle data
        """
        # Injected service
        self.pinnacle_service = pinnacle_service

        # Display model lists
        self.rois: List[ROIDisplayModel] = []
        self.pois: List[POIDisplayModel] = []
        self.beams: List[BeamDisplayModel] = []
        self.dose_model: DoseDisplayModel = DoseDisplayModel()

        # Callbacks
        self.on_overlay_changed: Optional[Callable[[], None]] = None

        # Refresh callbacks (set by views)
        self.refresh_rois: Optional[Callable[[], None]] = None
        self.refresh_pois: Optional[Callable[[], None]] = None
        self.refresh_beams: Optional[Callable[[], None]] = None
        self.refresh_dose: Optional[Callable[[], None]] = None

    @property
    def visible_rois(self) -> List[ROIDisplayModel]:
        """Get list of visible ROIs."""
        return [roi for roi in self.rois if roi.visible]

    @property
    def visible_pois(self) -> List[POIDisplayModel]:
        """Get list of visible POIs."""
        return [poi for poi in self.pois if poi.visible]

    @property
    def visible_beams(self) -> List[BeamDisplayModel]:
        """Get list of visible beams."""
        return [beam for beam in self.beams if beam.visible]

    @property
    def visible_isodose_lines(self) -> List[IsodoseDisplayModel]:
        """Get list of visible isodose lines."""
        return [iso for iso in self.dose_model.isodose_lines if iso.visible]

    def load_rois(self, rois: List) -> None:
        """Load ROI data from PinnacleService (already in DICOM coordinates).

        Args:
            rois: List of ROI objects from PinnacleService
        """
        try:
            self.rois = []
            color_palette = [
                "#FF0000", "#00FF00", "#0000FF", "#FFFF00",
                "#FF00FF", "#00FFFF", "#FFA500", "#800080",
                "#FFC0CB", "#A52A2A", "#808080", "#000080"
            ]

            for idx, roi in enumerate(rois):
                # Extract contour data
                contours = []
                if hasattr(roi, 'curve_list') and roi.curve_list:
                    for curve in roi.curve_list:
                        if hasattr(curve, 'points') and curve.points is not None:
                            # Convert numpy array to list of tuples
                            points = [(float(p[0]), float(p[1]), float(p[2])) for p in curve.points]
                            contours.append(points)

                display_model = ROIDisplayModel(
                    roi_id=getattr(roi, 'roi_number', idx + 1),
                    name=getattr(roi, 'name', f'ROI {idx + 1}'),
                    color=color_palette[idx % len(color_palette)],
                    visible=True,
                    contours=contours
                )
                self.rois.append(display_model)

            logger.info(f"Loaded {len(self.rois)} ROIs")

            # Trigger UI refresh
            if self.refresh_rois:
                self.refresh_rois()

            # Notify overlay change
            self._notify_change()

        except Exception as e:
            logger.error(f"Failed to load ROIs: {e}")
            self.rois = []

    def load_points(self, points: List) -> None:
        """Load POI data from PinnacleService (already in DICOM coordinates).

        Args:
            points: List of Point objects from PinnacleService
        """
        try:
            self.pois = []
            color_palette = [
                "#FF0000", "#00FF00", "#0000FF", "#FFFF00",
                "#FF00FF", "#00FFFF", "#FFA500", "#800080"
            ]

            for idx, point in enumerate(points):
                # Extract coordinates
                x_coord = getattr(point, 'x_coord', 0.0)
                y_coord = getattr(point, 'y_coord', 0.0)
                z_coord = getattr(point, 'z_coord', 0.0)

                display_model = POIDisplayModel(
                    poi_id=idx + 1,
                    name=getattr(point, 'name', f'Point {idx + 1}'),
                    coordinates=(x_coord, y_coord, z_coord),
                    color=color_palette[idx % len(color_palette)],
                    visible=True
                )
                self.pois.append(display_model)

            logger.info(f"Loaded {len(self.pois)} POIs")

            # Trigger UI refresh
            if self.refresh_pois:
                self.refresh_pois()

            # Notify overlay change
            self._notify_change()

        except Exception as e:
            logger.error(f"Failed to load POIs: {e}")
            self.pois = []

    def load_beams(self, beams: List) -> None:
        """Load beam data from trial.

        Args:
            beams: List of Beam objects from trial
        """
        try:
            self.beams = []

            for idx, beam in enumerate(beams):
                display_model = BeamDisplayModel(
                    beam_id=getattr(beam, 'beam_number', idx + 1),
                    name=getattr(beam, 'name', f'Beam {idx + 1}'),
                    gantry_angle=getattr(beam, 'gantry_angle', 0.0),
                    collimator_angle=getattr(beam, 'collimator_angle', 0.0),
                    couch_angle=getattr(beam, 'couch_angle', 0.0),
                    energy=getattr(beam, 'energy', 6.0),
                    visible=True
                )
                self.beams.append(display_model)

            logger.info(f"Loaded {len(self.beams)} beams")

            # Trigger UI refresh
            if self.refresh_beams:
                self.refresh_beams()

            # Notify overlay change
            self._notify_change()

        except Exception as e:
            logger.error(f"Failed to load beams: {e}")
            self.beams = []

    def load_dose(self, dose) -> None:
        """Load dose data from PinnacleService (already in DICOM coordinates).

        Args:
            dose: Dose object from PinnacleService
        """
        try:
            if dose is None:
                self.dose_model = DoseDisplayModel()
                return

            # Create isodose lines at standard percentages
            isodose_percentages = [95, 90, 80, 70, 50, 30, 20, 10]
            isodose_colors = [
                "#FF0000", "#FF8000", "#FFFF00", "#80FF00",
                "#00FF00", "#00FF80", "#00FFFF", "#0080FF"
            ]

            isodose_lines = []
            for idx, percentage in enumerate(isodose_percentages):
                iso_model = IsodoseDisplayModel(
                    level=percentage,
                    color=isodose_colors[idx % len(isodose_colors)],
                    visible=True
                )
                isodose_lines.append(iso_model)

            self.dose_model = DoseDisplayModel(
                reference_dose_cgy=3000.0,
                isodose_lines=isodose_lines
            )

            logger.info("Loaded dose data with isodose lines")

            # Trigger UI refresh
            if self.refresh_dose:
                self.refresh_dose()

            # Notify overlay change
            self._notify_change()

        except Exception as e:
            logger.error(f"Failed to load dose: {e}")
            self.dose_model = DoseDisplayModel()

    def _notify_change(self) -> None:
        """Notify listeners that overlay state changed."""
        if self.on_overlay_changed:
            self.on_overlay_changed()

    def load_mock_data(self) -> None:
        """Load mock overlay data for UI testing."""
        # Mock ROIs
        self.rois = [
            ROIDisplayModel(roi_id=1, name="PTV", color="#FF0000", visible=True),
            ROIDisplayModel(roi_id=2, name="Spinal Cord", color="#00FF00", visible=True),
            ROIDisplayModel(roi_id=3, name="Left Lung", color="#0000FF", visible=False),
            ROIDisplayModel(roi_id=4, name="Right Lung", color="#FFFF00", visible=False),
            ROIDisplayModel(roi_id=5, name="Heart", color="#FF00FF", visible=True),
        ]

        # Mock POIs
        self.pois = [
            POIDisplayModel(
                poi_id=1,
                name="Isocenter",
                color="#FFFFFF",
                visible=True,
                marker_style="cross",
            ),
            POIDisplayModel(
                poi_id=2,
                name="Reference Point",
                color="#FFFF00",
                visible=True,
                marker_style="circle",
            ),
        ]

        # Mock Beams
        self.beams = [
            BeamDisplayModel(
                beam_id=1,
                name="Beam 1",
                color="#00FFFF",
                visible=True,
                gantry_angle=0.0,
                energy="6 MV",
            ),
            BeamDisplayModel(
                beam_id=2,
                name="Beam 2",
                color="#00FFFF",
                visible=True,
                gantry_angle=120.0,
                energy="6 MV",
            ),
            BeamDisplayModel(
                beam_id=3,
                name="Beam 3",
                color="#00FFFF",
                visible=True,
                gantry_angle=240.0,
                energy="6 MV",
            ),
        ]

        # Mock Isodose lines
        self.dose_model = DoseDisplayModel(
            reference_dose_cgy=3000.0,
            isodose_lines=[
                IsodoseDisplayModel(level=105.0, color="#FF0000", visible=False),
                IsodoseDisplayModel(level=100.0, color="#FF6600", visible=True),
                IsodoseDisplayModel(level=95.0, color="#FFCC00", visible=True),
                IsodoseDisplayModel(level=90.0, color="#FFFF00", visible=False),
                IsodoseDisplayModel(level=80.0, color="#00FF00", visible=True),
                IsodoseDisplayModel(level=70.0, color="#00FFFF", visible=False),
                IsodoseDisplayModel(level=50.0, color="#0000FF", visible=True),
            ],
        )

        self._notify_change()

    def toggle_roi_visibility(self, roi_id: int) -> None:
        """Toggle ROI visibility.

        Args:
            roi_id: ID of the ROI to toggle
        """
        for roi in self.rois:
            if roi.roi_id == roi_id:
                roi.visible = not roi.visible
                self._notify_change()
                if self.refresh_rois:
                    self.refresh_rois()
                break

    def change_roi_color(self, roi_id: int, new_color: str) -> None:
        """Change ROI color.

        Args:
            roi_id: ID of the ROI to update
            new_color: New hex color code
        """
        for roi in self.rois:
            if roi.roi_id == roi_id:
                roi.color = new_color
                self._notify_change()
                if self.refresh_rois:
                    self.refresh_rois()
                break

    def toggle_poi_visibility(self, poi_id: int) -> None:
        """Toggle POI visibility.

        Args:
            poi_id: ID of the POI to toggle
        """
        for poi in self.pois:
            if poi.poi_id == poi_id:
                poi.visible = not poi.visible
                self._notify_change()
                if self.refresh_pois:
                    self.refresh_pois()
                break

    def change_poi_color(self, poi_id: int, new_color: str) -> None:
        """Change POI color.

        Args:
            poi_id: ID of the POI to update
            new_color: New hex color code
        """
        for poi in self.pois:
            if poi.poi_id == poi_id:
                poi.color = new_color
                self._notify_change()
                if self.refresh_pois:
                    self.refresh_pois()
                break

    def toggle_beam_visibility(self, beam_id: int) -> None:
        """Toggle beam visibility.

        Args:
            beam_id: ID of the beam to toggle
        """
        for beam in self.beams:
            if beam.beam_id == beam_id:
                beam.visible = not beam.visible
                self._notify_change()
                if self.refresh_beams:
                    self.refresh_beams()
                break

    def toggle_isodose_visibility(self, level: float) -> None:
        """Toggle isodose line visibility.

        Args:
            level: Dose level percentage to toggle
        """
        for isodose in self.dose_model.isodose_lines:
            if isodose.level == level:
                isodose.visible = not isodose.visible
                self._notify_change()
                if self.refresh_dose:
                    self.refresh_dose()
                break

    def set_reference_dose(self, dose_cgy: float) -> None:
        """Set reference dose.

        Args:
            dose_cgy: Reference dose in centiGray
        """
        self.dose_model.reference_dose_cgy = dose_cgy
        self._notify_change()
        if self.refresh_dose:
            self.refresh_dose()

    def _notify_change(self) -> None:
        """Notify listeners that overlay state changed."""
        if self.on_overlay_changed:
            self.on_overlay_changed()

    def reset(self) -> None:
        """Reset all state to initial values."""
        self.rois = []
        self.pois = []
        self.beams = []
        self.dose_model = DoseDisplayModel()

        if self.refresh_rois:
            self.refresh_rois()
        if self.refresh_pois:
            self.refresh_pois()
        if self.refresh_beams:
            self.refresh_beams()
        if self.refresh_dose:
            self.refresh_dose()
