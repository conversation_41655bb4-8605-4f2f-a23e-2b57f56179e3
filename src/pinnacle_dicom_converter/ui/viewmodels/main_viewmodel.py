"""Main ViewModel orchestrating all child ViewModels and data flow.

This ViewModel serves as the central coordinator for the MVVM architecture,
managing the relationships between all child ViewModels and the PinnacleService.
"""

from typing import Optional
import logging

from pinnacle_dicom_converter.ui.viewmodels.navigation_viewmodel import NavigationViewModel
from pinnacle_dicom_converter.ui.viewmodels.image_viewmodel import Image<PERSON>iewModel
from pinnacle_dicom_converter.ui.viewmodels.overlay_viewmodel import OverlayViewModel
from pinnacle_dicom_converter.ui.viewmodels.toolbar_viewmodel import ToolbarViewModel
from pinnacle_dicom_converter.ui.services.pinnacle_service import PinnacleService

logger = logging.getLogger(__name__)


class MainViewModel:
    """Main orchestrator for all ViewModels and data flow.
    
    This ViewModel coordinates the interaction between all child ViewModels
    and manages the overall application state. It handles:
    - Service injection into child ViewModels
    - Data flow coordination between ViewModels
    - Application lifecycle management
    - Error handling and user feedback
    
    Attributes:
        pinnacle_service: Service for accessing Pinnacle data
        navigation_vm: ViewModel for patient/plan/trial navigation
        image_vm: ViewModel for CT image display
        overlay_vm: ViewModel for ROI/POI/Beam/Dose overlays
        toolbar_vm: ViewModel for toolbar state and actions
    """
    
    def __init__(self, pinnacle_service: Optional[PinnacleService] = None):
        """Initialize the main ViewModel with service injection.
        
        Args:
            pinnacle_service: Optional service instance for dependency injection
        """
        # Initialize service
        self.pinnacle_service = pinnacle_service or PinnacleService()
        
        # Initialize child ViewModels
        self.navigation_vm = NavigationViewModel(self.pinnacle_service)
        self.image_vm = ImageViewModel(self.pinnacle_service)
        self.overlay_vm = OverlayViewModel(self.pinnacle_service)
        self.toolbar_vm = ToolbarViewModel()
        
        # Wire up callbacks between ViewModels
        self._setup_callbacks()
        
        logger.info("MainViewModel initialized")
    
    def _setup_callbacks(self) -> None:
        """Set up callbacks and data flow between ViewModels."""
        
        # Navigation callbacks
        self.navigation_vm.on_patient_changed = self._on_patient_changed
        self.navigation_vm.on_plan_changed = self._on_plan_changed
        self.navigation_vm.on_trial_changed = self._on_trial_changed
        
        # Toolbar callbacks
        self.toolbar_vm.on_open_directory = self._on_open_directory
        self.toolbar_vm.on_open_archive = self._on_open_archive
        self.toolbar_vm.on_data_closed = self._on_data_closed
        
        # Overlay callbacks
        self.overlay_vm.on_overlay_changed = self._on_overlay_changed
        
        logger.debug("ViewModel callbacks configured")
    
    def _on_patient_changed(self, patient_id: int) -> None:
        """Handle patient selection change.
        
        Args:
            patient_id: Selected patient ID
        """
        logger.info(f"Patient changed to: {patient_id}")
        
        # Update toolbar state
        self.toolbar_vm.has_data_loaded = True
        self.toolbar_vm.is_selection_complete = False
        
        # Clear downstream ViewModels
        self.image_vm.reset()
        self.overlay_vm.reset()
    
    def _on_plan_changed(self, plan_id: int) -> None:
        """Handle plan selection change.
        
        Args:
            plan_id: Selected plan ID
        """
        logger.info(f"Plan changed to: {plan_id}")
        
        # Load CT image for the plan
        patient_id = self.navigation_vm.selected_patient_id
        if patient_id is not None:
            # Load the first image set (typically the planning CT)
            image_set = self.pinnacle_service.get_image_set(patient_id, 0, load_pixel_data=True)
            if image_set:
                self.image_vm.load_image_set(image_set)
                logger.info("CT image set loaded successfully")
            else:
                logger.warning("Failed to load CT image set")
            
            # Load ROIs and POIs for the plan
            rois = self.pinnacle_service.get_rois(patient_id, plan_id)
            points = self.pinnacle_service.get_points(patient_id, plan_id)
            
            self.overlay_vm.load_rois(rois)
            self.overlay_vm.load_points(points)
            
            logger.info(f"Loaded {len(rois)} ROIs and {len(points)} points")
        
        # Update toolbar state
        self.toolbar_vm.is_selection_complete = False
    
    def _on_trial_changed(self, trial_id: int) -> None:
        """Handle trial selection change.
        
        Args:
            trial_id: Selected trial ID
        """
        logger.info(f"Trial changed to: {trial_id}")
        
        # Load dose data for the trial
        patient_id = self.navigation_vm.selected_patient_id
        plan_id = self.navigation_vm.selected_plan_id
        
        if patient_id is not None and plan_id is not None:
            dose = self.pinnacle_service.get_dose(patient_id, plan_id, trial_id)
            if dose:
                self.overlay_vm.load_dose(dose)
                logger.info("Dose data loaded successfully")
            else:
                logger.warning("Failed to load dose data")
            
            # Load beam data from trials
            trials = self.pinnacle_service.get_trials(patient_id, plan_id)
            selected_trial = None
            for trial in trials:
                if trial.trial_id == trial_id:
                    selected_trial = trial
                    break
            
            if selected_trial and hasattr(selected_trial, 'beam_list'):
                self.overlay_vm.load_beams(selected_trial.beam_list)
                logger.info(f"Loaded {len(selected_trial.beam_list)} beams")
        
        # Update toolbar - selection is now complete
        self.toolbar_vm.is_selection_complete = True
    
    def _on_overlay_changed(self) -> None:
        """Handle overlay state changes (visibility, colors, etc.)."""
        # Regenerate SVG overlays in the image viewer
        self.image_vm.update_svg_overlays(self.overlay_vm)
        logger.debug("SVG overlays updated")
    
    def _on_open_directory(self) -> bool:
        """Handle opening a directory data source.
        
        Returns:
            True if successful, False otherwise
        """
        # For now, use test data - in real implementation this would open a file dialog
        test_data_path = "/home/<USER>/source/repos/arrow/python/pinnacle-dicom-converter/tests/data/01"
        return self._load_data_source(test_data_path)
    
    def _on_open_archive(self) -> bool:
        """Handle opening an archive data source.
        
        Returns:
            True if successful, False otherwise
        """
        # For now, use test data - in real implementation this would open a file dialog
        test_data_path = "/home/<USER>/source/repos/arrow/python/pinnacle-dicom-converter/tests/data/01.tar.gz"
        return self._load_data_source(test_data_path)
    
    def _load_data_source(self, data_source_path: str) -> bool:
        """Load a data source and initialize navigation.
        
        Args:
            data_source_path: Path to the data source
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Load the data source
            success = self.pinnacle_service.load_data_source(data_source_path)
            if not success:
                logger.error(f"Failed to load data source: {data_source_path}")
                return False
            
            # Initialize navigation with patient data
            self.navigation_vm.load_patients_from_service()
            
            # Update toolbar state
            self.toolbar_vm.has_data_loaded = True
            self.toolbar_vm.is_selection_complete = False
            
            logger.info(f"Successfully loaded data source: {data_source_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error loading data source {data_source_path}: {e}")
            return False
    
    def _on_data_closed(self) -> None:
        """Handle data source closing."""
        logger.info("Closing data source")
        
        # Close the service
        self.pinnacle_service.close_data_source()
        
        # Reset all ViewModels
        self.navigation_vm.reset()
        self.image_vm.reset()
        self.overlay_vm.reset()
        
        # Update toolbar state
        self.toolbar_vm.has_data_loaded = False
        self.toolbar_vm.is_selection_complete = False
        
        logger.info("Data source closed successfully")
    
    def get_current_selection_info(self) -> dict:
        """Get information about current selection for display.
        
        Returns:
            Dictionary with current selection information
        """
        return {
            'patient_id': self.navigation_vm.selected_patient_id,
            'plan_id': self.navigation_vm.selected_plan_id,
            'trial_id': self.navigation_vm.selected_trial_id,
            'has_data': self.toolbar_vm.has_data_loaded,
            'selection_complete': self.toolbar_vm.is_selection_complete
        }
    
    def auto_load_test_data(self) -> bool:
        """Auto-load test data for development/testing.
        
        Returns:
            True if successful, False otherwise
        """
        test_data_path = "/home/<USER>/source/repos/arrow/python/pinnacle-dicom-converter/tests/data/01"
        return self._load_data_source(test_data_path)
